{"name": "proddy", "version": "1.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint", "lint:fix": "eslint --fix", "format": "prettier --check . --config .prettierrc.mjs", "format:fix": "prettier --write . --config .prettierrc.mjs"}, "author": {"name": "<PERSON>", "url": "https://github.com/george-bobby"}, "description": "Collaborate with your team using real-time messaging in this app built with Next.js, Convex, and Shadcn UI.", "keywords": ["reactjs", "nextjs", "convex", "next-auth", "emoji-picker-react", "lucide-icons", "react-icons", "quill-editor", "shadcn-ui", "radix-ui", "tailwindcss", "nuqs", "sonner", "typescript", "javascript", "vercel", "postcss", "prettier", "eslint", "react-dom", "html", "css", "state-management", "real-time-messaging", "collaboration", "ui/ux", "date-fns", "cn", "clsx", "lucide-react"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/george-bobby/proddy-platform.git"}, "homepage": "https://github.com/george-bobby/proddy-platform#readme", "bugs": {"url": "https://github.com/george-bobby/proddy-platform/issues"}, "dependencies": {"@ai-sdk/google": "^1.2.14", "@ai-sdk/openai": "^1.3.22", "@auth/core": "^0.34.2", "@convex-dev/auth": "^0.0.65", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@google/generative-ai": "^0.24.0", "@hotjar/browser": "^1.0.9", "@langchain/core": "^0.3.57", "@liveblocks/node": "^2.24.1", "@liveblocks/react": "^2.24.1", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.1.2", "@radix-ui/react-visually-hidden": "^1.1.0", "@react-email/components": "^0.0.39", "@stream-io/video-react-sdk": "^1.15.0", "ai": "^4.3.10", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "composio-core": "^0.5.39", "convex": "^1.23.0", "date-fns": "^4.1.0", "docx": "^9.4.1", "dotenv": "^16.5.0", "emoji-picker-react": "^4.12.0", "framer-motion": "^12.11.0", "jotai": "^2.9.3", "jspdf": "^3.0.1", "lucide-react": "^0.439.0", "moment": "^2.30.1", "next": "14.2.26", "next-pwa": "^5.6.0", "next-themes": "^0.3.0", "nuqs": "^1.19.1", "openai": "^4.103.0", "perfect-freehand": "^1.2.2", "quill": "^2.0.2", "react": "^18", "react-calendar-timeline": "^0.30.0-beta.3", "react-contenteditable": "^3.3.7", "react-day-picker": "^9.6.7", "react-dom": "^18", "react-icons": "^5.3.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.2", "react-use": "^17.5.1", "react-verification-input": "^4.1.2", "remark-gfm": "^4.0.1", "resend": "^4.5.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@babel/eslint-parser": "^7.25.1", "@tailwindcss/typography": "^0.5.16", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.8", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "postcss": "^8", "prettier-plugin-tailwindcss": "^0.6.6", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}